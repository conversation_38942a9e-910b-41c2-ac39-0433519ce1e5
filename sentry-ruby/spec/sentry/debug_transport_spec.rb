# frozen_string_literal: true

RSpec.describe Sentry::DebugTransport do
  let(:configuration) do
    Sentry::Configuration.new.tap do |config|
      config.sdk_logger = Logger.new(nil)
      config.dsn = "http://test@localhost:9999/123"
      config.sdk_debug_transport_log_file = "/tmp/test_sentry_debug.log"
    end
  end

  let(:client) { Sentry::Client.new(configuration) }
  let(:transport) { described_class.new(configuration) }

  after do
    transport.clear
  end

  describe "#initialize" do
    it "creates the log file and clears it" do
      expect(File.exist?(transport.log_file_path)).to be true
      expect(File.read(transport.log_file_path)).to be_empty
    end

    it "uses the configured log file path" do
      expect(transport.log_file_path).to eq(configuration.sdk_debug_transport_log_file)
    end

    context "with real DSN" do
      let(:configuration) do
        Sentry::Configuration.new.tap do |config|
          config.dsn = "https://<EMAIL>/123"
          config.sdk_debug_transport_log_file = "/tmp/test_sentry_debug.log"
        end
      end

      it "initializes HTTP transport for real DSN" do
        expect(transport.http_transport).to be_a(Sentry::HTTPTransport)
      end
    end

    context "with localhost DSN" do
      it "does not initialize HTTP transport for localhost DSN" do
        expect(transport.http_transport).to be_nil
      end
    end
  end

  describe "#send_event" do
    let(:event) { client.event_from_exception(ZeroDivisionError.new("divided by 0")) }

    it "logs the event to the file" do
      transport.send_event(event)

      expect(transport.events.count).to eq(1)

      logged_event = transport.events.first
      expect(logged_event).to include("timestamp", "envelope_headers", "items")
      expect(logged_event["items"]).to be_an(Array)
      expect(logged_event["items"].first["headers"]["type"]).to eq("event")
    end

    it "returns the event" do
      result = transport.send_event(event)
      expect(result).to eq(event)
    end
  end

  describe "#events" do
    let(:event) { client.event_from_exception(ZeroDivisionError.new("divided by 0")) }

    it "returns empty array when no events" do
      expect(transport.events).to eq([])
    end

    it "returns logged events as hashes" do
      transport.send_event(event)

      events = transport.events
      expect(events.length).to eq(1)
      expect(events.first).to be_a(Hash)
      expect(events.first).to include("timestamp", "envelope_headers", "items")
    end

    it "handles multiple events" do
      transport.send_event(event)
      transport.send_event(client.event_from_message("test message"))

      expect(transport.events.length).to eq(2)
      expect(transport.events.count).to eq(2)
    end
  end

  describe "#clear" do
    let(:event) { client.event_from_exception(ZeroDivisionError.new("divided by 0")) }

    it "clears all logged events" do
      transport.send_event(event)
      expect(transport.events.count).to eq(1)

      transport.clear
      expect(transport.events.count).to eq(0)
      expect(transport.events).to be_empty
    end
  end

  describe "#events count" do
    let(:event) { client.event_from_exception(ZeroDivisionError.new("divided by 0")) }

    it "returns the number of logged events" do
      expect(transport.events.count).to eq(0)

      transport.send_event(event)
      expect(transport.events.count).to eq(1)

      transport.send_event(client.event_from_message("test"))
      expect(transport.events.count).to eq(2)
    end
  end
end
