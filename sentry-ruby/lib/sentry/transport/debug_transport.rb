# frozen_string_literal: true

require "json"
require "fileutils"

module Sentry
  # DebugTransport is a transport that logs events to a file for debugging purposes.
  #
  # It can optionally also send events to Sentry via HTTP transport if a real DSN
  # is provided.
  class DebugTransport < Transport
    attr_reader :log_file_path, :http_transport

    def initialize(configuration)
      super

      @log_file_path = configuration.sdk_debug_transport_log_file || default_log_file_path

      FileUtils.mkdir_p(File.dirname(@log_file_path))

      log_debug("DebugTransport: Initialized with log file: #{@log_file_path}")

      if configuration.dsn && !configuration.dsn.to_s.include?("localhost")
        @http_transport = Sentry::HTTPTransport.new(configuration)
        log_debug("DebugTransport: Initialized with HTTP transport for DSN: #{configuration.dsn}")
      else
        @http_transport = nil
        log_debug("DebugTransport: Using local-only mode for DSN: #{configuration.dsn}")
      end
    end

    def send_event(event)
      envelope = envelope_from_event(event)
      send_envelope(envelope)
      event
    end

    def send_envelope(envelope)
      envelope_data = {
        timestamp: Time.now.utc.iso8601,
        envelope_headers: envelope.headers,
        items: envelope.items.map do |item|
          {
            headers: item.headers,
            payload: item.payload
          }
        end
      }

      File.open(log_file_path, "a") do |file|
        puts "Dumping event"
        file << JSON.dump(envelope_data)
      end

      if http_transport
        http_transport.send_envelope(envelope)
      end
    end

    def events
      return [] unless File.exist?(log_file_path)

      File.readlines(log_file_path).map do |line|
        JSON.load(line)
      end
    end

    def clear
      File.write(log_file_path, "")
      log_debug("DebugTransport: Cleared events from #{log_file_path}")
    end

    private

    def default_log_file_path
      if defined?(Rails) && Rails.respond_to?(:root) && Rails.root
        File.join(Rails.root, "log", "sentry_debug_events.log")
      elsif File.directory?("log")
        File.join("log", "sentry_debug_events.log")
      else
        "/tmp/sentry_debug_events.json"
      end
    end
  end
end
